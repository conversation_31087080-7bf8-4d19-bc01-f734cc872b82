class LocalizationKeys {
  static const fullAppName = 'full_app_name';
  static const plzWait = 'plz_wait';
  static const welcomeBack = 'welcome_back';
  static const required = 'required';
  static const next = 'next';
  static const emailInvalid = 'email_invalid';
  static const passInvalid = 'password_invalid';
  static const passMatch = 'password_match';
  static const userLoggedOut = 'user_logged_out';
  static const home = 'home';
  static const services = 'services';
  static const profile = 'profile';
  static const contactUs = 'contact_us';
  static const settings = 'settings';
  static const logout = 'logout';
  static const showMenu = 'show_menu';
  static const notifications = 'Notifications';
  static const submit = 'submit';
  static const noData = 'no_data';
  static const connectedToInternet = 'connected_to_internet';
  static const notConnectedCheckYourInternet =
      'not_connected_check_your_internet';
  static const requestToApiServerWasCancelled =
      'request_to_api_server_was_cancelled';
  static const unknownError = 'unknown_error';
  static const connectionTimeoutWithApiServer =
      'connection_timeout_with_api_server';
  static const receiveTimeoutInConnectionWithApiServer =
      'receive_timeout_in_connection_with_api_server';
  static const badRequest = 'bad_request';
  static const unauthorized = 'unauthorized';
  static const forbidden = 'forbidden';
  static const internalServerError = 'internal_server_error';
  static const badGateway = 'bad_gateway';
  static const sendTimeoutInConnectionWithApiServer =
      'send_timeout_in_connection_with_api_server';
  static const noInternet = 'no_internet';
  static const couldNotLaunch = 'could_not_launch';
  static const unexpectedErrorOccurred = 'unexpected_error_occurred';
  static const somethingWentWrong = 'something_went_wrong';
  static const theWebsiteUrlIsInvalid = 'the_website_url_is_invalid';
  static const wrongFormat = 'wrong_format';
  static const enterNumberlessThan = 'enter_numberless_than';
  static const phoneNumberInvalid = 'phone_number_invalid';
  static const nameInvalid = 'name_invalid';
  static const checkOtpCode = 'check_otp_code';
  static const phoneNumberRequired = "phone_number_required";
  static const emailRequired = "email_required";
  static const passwordRequired = "password_required";

  // Login and authentication keys
  static const login = 'login';
  static const userName = 'user_name';
  static const userNameHint = 'user_name_hint';
  static const password = 'password';
  static const passwordHint = 'password_hint';
  static const forgotPassword = 'forgot_password';
  static const clickToReset = 'click_to_reset';
  static const dontHaveAccount = 'dont_have_account';
  static const createAccount = 'create_account';
  static const orThrough = 'or_through';
  static const uaePassRegister = 'uae_pass_register';
  static const copyrightText = 'copyright_text';
  static const usernameRequired = 'username_required';
  static const hello = 'hello';
  static const welcomeUser = 'welcome_user';
  static const defaultEmail = 'default_email';

  // Home screen keys
  static const registeredChildren = 'registered_children';
  static const unregisteredChildren = 'unregistered_children';
  static const sonsWithdrawn = 'sons_withdrawn';
  static const sonsFinishedStudying = 'sons_finished_studying';
  static const addNewSon = 'add_new_son';

  static const fullNameRequired = 'full_name_required';
  static const fullNameHint = 'full_name_hint';
  static const idNumber = 'id_number';
  static const idNumberHint = 'id_number_hint';
  static const nationalityField = 'nationality_field';
  static const nationalityHint = 'nationality_hint';
  static const grade = 'grade';
  static const gradeHint = 'grade_hint';
  static const requiredDocuments = 'required_documents';
  static const personalPhoto = 'personal_photo';
  static const uploadPdfImage = 'upload_pdf_image';
  static const notes = 'notes';
  static const notesHint = 'notes_hint';
  static const saveButton = 'save_button';
  static const requiredField = 'required_field';
  static const delegation = 'delegation';
  static const certificates = 'certificates';
  static const requests = 'requests';
  static const showAll = 'show_all';
  static const requestType = 'request_type';
  static const requestStatus = 'request_status';
  static const search = 'search';
  static const reset = 'reset';
  static const requestNumber = 'request_number';
  static const requestRelatedTo = 'request_related_to';
  static const school = 'school';
  static const curriculum = 'curriculum';
  static const createdBy = 'created_by';
  static const requestDate = 'request_date';
  static const modificationDate = 'modification_date';
  static const details = 'details';
  static const pending = 'pending';
  static const academicSequenceCertificate = 'academic_sequence_certificate';
  static const noRequestsFound = 'no_requests_found';
  static const notAvailable = 'not_available';
  static const pay = 'pay';
  static const view = 'view';
  static const rejected = 'rejected';
  static const approved = 'approved';
  static const completed = 'completed';
  static const paid = 'paid';
  static const free = 'free';
  static const pendingProcessing = 'pending_processing';
  static const approvedStatus = 'approved_status';
  static const readyForDownload = 'ready_for_download';
  static const rejectedStatus = 'rejected_status';
  static const paidStatus = 'paid_status';
  static const paymentRequired = 'payment_required';
  static const feesAmount = 'fees_amount';
  static const currency = 'currency';
  static const pendingReview = 'pending_review';
  static const completedStatus = 'completed_status';

  // Onboarding keys
  static const onboardingTitle1 = 'onboarding_title_1';
  static const onboardingSubtitle1 = 'onboarding_subtitle_1';
  static const onboardingTitle2 = 'onboarding_title_2';
  static const onboardingSubtitle2 = 'onboarding_subtitle_2';
  static const onboardingTitle3 = 'onboarding_title_3';
  static const onboardingSubtitle3 = 'onboarding_subtitle_3';

  // Main Tabs keys
  static const homeTab = 'home_tab';
  static const certificatesTab = 'certificates_tab';
  static const requestsTab = 'requests_tab';
  static const chatsTab = 'chats_tab';

  // Profile screen keys
  static const myProfile = 'my_profile';
  static const editProfile = 'edit_profile';
  static const familyName = 'family_name';
  static const nationality = 'nationality';
  static const gender = 'gender';
  static const mobileNumber = 'mobile_number';
  static const phoneNumber = 'phone_number';
  static const registrationDate = 'registration_date';
  static const logoutButton = 'logout_button';
  static const male = 'male';
  static const female = 'female';
  static const emirati = 'emirati';
  static const position = 'position';
  static const logoutConfirmation = 'logoutConfirmation';
  static const confirm = 'confirm';
  static const cancel = 'cancel';
  static const guardian = 'guardian';
  static const defaultProfileName = 'defaultProfileName';
  static const defaultProfilePhone = 'defaultProfilePhone';
  static const defaultProfileDate = 'defaultProfileDate';
}
