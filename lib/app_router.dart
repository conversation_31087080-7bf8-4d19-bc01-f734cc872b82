import 'package:daleel/features/main_taps/screens/main_taps/presentation/screen/main_taps_screen.dart';
import 'package:daleel/features/profile/presentation/screen/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import 'features/login/presentation/screen/login_screen.dart';
import 'features/onboarding/presentation/bloc/onboarding_bloc.dart';
import 'features/onboarding/presentation/screens/onboarding_screen.dart';
import 'features/splash/presentation/screen/splash_screen.dart';
import 'features/add_son/presentation/screen/add_son_selection_screen.dart';
// import 'features/add_son/presentation/screen/add_son_with_id_screen.dart';
// import 'features/add_son/presentation/screen/add_son_without_id_screen.dart';

class AppRouter {
  /// main Navigation Router
  static final GlobalKey<NavigatorState> mainNavigatorKey =
      GlobalKey<NavigatorState>();

  static GoRouter router = GoRouter(
    debugLogDiagnostics: true,
    navigatorKey: mainNavigatorKey,
    initialLocation: SplashScreen.routeName,
    routes: <RouteBase>[
      GoRoute(path: '/', redirect: (context, state) => SplashScreen.routeName),
      GoRoute(
        path: SplashScreen.routeName,
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: LoginScreen.routeName,
        builder: (context, state) => LoginScreen(),
      ),
      GoRoute(
        path: MainTapsScreen.routeName,
        builder: (context, state) => const MainTapsScreen(),
      ),
      GoRoute(
        path: ProfileScreen.routeName,
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: OnboardingScreen.routeName,
        builder:
            (context, state) => BlocProvider(
              create: (_) => OnboardingBloc(),
              child: const OnboardingScreen(),
            ),
      ),
      GoRoute(
        path: '/add-son-selection',
        builder: (context, state) => const AddSonSelectionScreen(),
      ),
      // GoRoute(
      //   path: '/add-son-with-id',
      //   builder: (context, state) => const AddSonWithIdScreen(),
      // ),
      // GoRoute(
      //   path: '/add-son-without-id',
      //   builder: (context, state) => const AddSonWithoutIdScreen(),
      // ),
    ],
  );
}
