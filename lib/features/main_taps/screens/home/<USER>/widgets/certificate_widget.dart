import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/certificate_ui_model.dart';
import 'package:daleel/res/app_asset_paths.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CertificateWidget extends StatelessWidget {
  final CertificateUiModel? certificate;

  const CertificateWidget({super.key, this.certificate});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: AppColors.colorCertificateBackground,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(AppAssetPaths.certificate, height: 40, width: 40),

          SizedBox(width: 10.w),

          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                certificate?.certificateName ?? 'Evaluation Certificate',
                style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 14,
                  color: AppColors.headlineSmall,
                ),
              ),
              Text(
                certificate?.studentName ?? 'Mohamed Ahmed Mahmoud',
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.headlineSmall,
                ),
              ),
            ],
          ),

          Spacer(),

          Container(
            width: 60,
            height: 40,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.r),
              color: AppColors.colorSecondary,
            ),
            child: Center(
              child: Text(
                _getButtonText(context),
                style: TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                  color: AppColors.whiteIcon,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getButtonText(BuildContext context) {
    if (certificate == null) return context.translate(LocalizationKeys.pay);

    switch (certificate!.status.toLowerCase()) {
      case 'payed':
        return context.translate(LocalizationKeys.view);
      case 'paymentstarted':
        return context.translate(LocalizationKeys.pay);
      case 'pending':
        return context.translate(LocalizationKeys.pending);
      case 'rejected':
        return context.translate(LocalizationKeys.rejected);
      default:
        return context.translate(LocalizationKeys.pay);
    }
  }
}
