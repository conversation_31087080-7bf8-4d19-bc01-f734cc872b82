import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/widgets/app_buttons/app_elevated_button.dart';
import 'package:daleel/res/app_colors.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';

class HomeButtonsSection extends StatelessWidget {
  const HomeButtonsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: () => _onAddNewSonPressed(context),
              title: context.translate(LocalizationKeys.addNewSon),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
          Si<PERSON><PERSON><PERSON>(height: 20.h),
          SizedBox(
            height: 50.h,
            width: double.infinity,
            child: AppElevatedButton.withTitle(
              shape: RoundedRectangleBorder(
                side: BorderSide(color: AppColors.colorSecondary, width: 1.5),
                borderRadius: BorderRadius.circular(16),
              ),
              onPressed: () => _onDelegationPressed(),
              color: Colors.transparent,
              textColor: AppColors.colorSecondary,
              title: context.translate(LocalizationKeys.delegation),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  void _onAddNewSonPressed(BuildContext context) {
    // context.push('/add-son-selection');
  }

  void _onDelegationPressed() {}
}
