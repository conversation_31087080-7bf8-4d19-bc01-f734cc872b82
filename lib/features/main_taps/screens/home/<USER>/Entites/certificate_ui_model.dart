import 'package:daleel/features/main_taps/screens/home/<USER>/models/certificate_api_model.dart';
import 'package:daleel/features/main_taps/screens/home/<USER>/Entites/certificate_status.dart';

class CertificateUiModel {
  final int id;
  final int commonCertificateRequestId;
  final int schoolYearId;
  final String certificateName;
  final String certificateType;
  final int studentId;
  final String studentName;
  final String status;
  final double fees;
  final String? url;
  final String? paymentRefNo;
  final bool createdBySpeaAdmin;
  final String? paymentReceiptNo;
  final DateTime? certificateIssuanceDate;
  final bool isDownloadable; // UI-specific property
  final String displayStatus; // Formatted status for UI
  final String formattedFees; // Formatted fees for UI

  const CertificateUiModel({
    required this.id,
    required this.commonCertificateRequestId,
    required this.schoolYearId,
    required this.certificateName,
    required this.certificateType,
    required this.studentId,
    required this.studentName,
    required this.status,
    required this.fees,
    this.url,
    this.paymentRefNo,
    required this.createdBySpeaAdmin,
    this.paymentReceiptNo,
    this.certificateIssuanceDate,
    required this.isDownloadable,
    required this.displayStatus,
    required this.formattedFees,
  });

  factory CertificateUiModel.fromCertificateApiModel(
    CertificateApiModel apiModel,
  ) {
    return CertificateUiModel(
      id: apiModel.id,
      commonCertificateRequestId: apiModel.commonCertificateRequestId,
      schoolYearId: apiModel.schoolYearId,
      certificateName: apiModel.certificateName.en,
      certificateType: apiModel.certificateType,
      studentId: apiModel.studentId,
      studentName: apiModel.studentName.en,
      status: apiModel.status,
      fees: apiModel.fees,
      url: apiModel.url,
      paymentRefNo: apiModel.paymentRefNo,
      createdBySpeaAdmin: apiModel.createdBySpeaAdmin,
      paymentReceiptNo: apiModel.paymentReceiptNo,
      certificateIssuanceDate: _parseDate(apiModel.certificateIssuanceDate),
      isDownloadable: _isDownloadable(apiModel.status, apiModel.url),
      displayStatus:
          CertificateStatus.fromApiKey(apiModel.status).getDisplayStatus(),
      formattedFees: CertificateStatus.formatFeesWithAmount(apiModel.fees),
    );
  }

  static bool _isDownloadable(String status, String? url) {
    return status.toLowerCase() == 'completed' && url != null && url.isNotEmpty;
  }

  static DateTime? _parseDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return null;
    }
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  CertificateUiModel copyWith({
    int? id,
    int? commonCertificateRequestId,
    int? schoolYearId,
    String? certificateName,
    String? certificateType,
    int? studentId,
    String? studentName,
    String? status,
    double? fees,
    String? url,
    String? paymentRefNo,
    bool? createdBySpeaAdmin,
    String? paymentReceiptNo,
    DateTime? certificateIssuanceDate,
    bool? isDownloadable,
    String? displayStatus,
    String? formattedFees,
  }) {
    return CertificateUiModel(
      id: id ?? this.id,
      commonCertificateRequestId:
          commonCertificateRequestId ?? this.commonCertificateRequestId,
      schoolYearId: schoolYearId ?? this.schoolYearId,
      certificateName: certificateName ?? this.certificateName,
      certificateType: certificateType ?? this.certificateType,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      status: status ?? this.status,
      fees: fees ?? this.fees,
      url: url ?? this.url,
      paymentRefNo: paymentRefNo ?? this.paymentRefNo,
      createdBySpeaAdmin: createdBySpeaAdmin ?? this.createdBySpeaAdmin,
      paymentReceiptNo: paymentReceiptNo ?? this.paymentReceiptNo,
      certificateIssuanceDate:
          certificateIssuanceDate ?? this.certificateIssuanceDate,
      isDownloadable: isDownloadable ?? this.isDownloadable,
      displayStatus: displayStatus ?? this.displayStatus,
      formattedFees: formattedFees ?? this.formattedFees,
    );
  }
}

class CertificateListUiData {
  final int totalAllData;
  final int total;
  final List<CertificateUiModel> certificates;

  const CertificateListUiData({
    required this.totalAllData,
    required this.total,
    required this.certificates,
  });

  factory CertificateListUiData.fromCertificateResponse(
    CertificateResponse response,
  ) {
    return CertificateListUiData(
      totalAllData: response.totalAllData,
      total: response.total,
      certificates:
          response.data
              .map(
                (apiModel) =>
                    CertificateUiModel.fromCertificateApiModel(apiModel),
              )
              .toList(),
    );
  }
}
