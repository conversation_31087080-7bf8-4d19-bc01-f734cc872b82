import 'package:daleel/core/extensions/extension_localization.dart';
import 'package:daleel/features/widgets/page_header/page_header.dart';
import 'package:daleel/utils/locale/app_localization_keys.dart';
import 'package:flutter/material.dart';

class RequestsScreen extends StatelessWidget {
  const RequestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          PageHeader(title: context.translate(LocalizationKeys.requests)),
        ],
      ),
    );
  }
}
