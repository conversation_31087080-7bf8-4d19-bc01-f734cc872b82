class AddSonSendModel {
  final String nationalId;
  final String? studentId;
  final String firstName;
  final String lastName;
  final String arabicFirstName;
  final String arabicLastName;
  final DateTime? dateOfBirth;
  final String? gender;
  final bool hasStudentId;

  AddSonSendModel({
    required this.nationalId,
    this.studentId,
    required this.firstName,
    required this.lastName,
    required this.arabicFirstName,
    required this.arabicLastName,
    this.dateOfBirth,
    this.gender,
    required this.hasStudentId,
  });

  Map<String, dynamic> toJson() {
    return {
      'nationalId': nationalId,
      'studentId': studentId,
      'firstName': firstName,
      'lastName': lastName,
      'arabicFirstName': arabicFirstName,
      'arabicLastName': arabicLastName,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'gender': gender,
      'hasStudentId': hasStudentId,
    };
  }

  factory AddSonSendModel.fromJson(Map<String, dynamic> json) {
    return AddSonSendModel(
      nationalId: json['nationalId'] as String,
      studentId: json['studentId'] as String?,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      arabicFirstName: json['arabicFirstName'] as String,
      arabicLastName: json['arabicLastName'] as String,
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth'] as String)
          : null,
      gender: json['gender'] as String?,
      hasStudentId: json['hasStudentId'] as bool,
    );
  }
}
