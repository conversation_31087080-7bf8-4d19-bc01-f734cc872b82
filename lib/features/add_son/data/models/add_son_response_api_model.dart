class AddSonResponseApiModel {
  final int id;
  final SonName name;
  final String nationalId;
  final String? studentId;
  final bool isRegistered;
  final String status;
  final DateTime createdDate;

  AddSonResponseApiModel({
    required this.id,
    required this.name,
    required this.nationalId,
    this.studentId,
    required this.isRegistered,
    required this.status,
    required this.createdDate,
  });

  factory AddSonResponseApiModel.fromJson(Map<String, dynamic> json) {
    return AddSonResponseApiModel(
      id: json['id'] as int,
      name: SonName.fromJson(json['name'] as Map<String, dynamic>),
      nationalId: json['nationalId'] as String,
      studentId: json['studentId'] as String?,
      isRegistered: json['isRegistered'] as bool,
      status: json['status'] as String,
      createdDate: DateTime.parse(json['createdDate'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name.toJson(),
      'nationalId': nationalId,
      'studentId': studentId,
      'isRegistered': isRegistered,
      'status': status,
      'createdDate': createdDate.toIso8601String(),
    };
  }
}

class SonName {
  final String en;
  final String ar;

  SonName({required this.en, required this.ar});

  factory SonName.fromJson(Map<String, dynamic> json) {
    return SonName(
      en: json['en'] as String,
      ar: json['ar'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'en': en,
      'ar': ar,
    };
  }
}
